'use client';

import { useParams } from 'next/navigation';
import { Frame } from '../../components/page/screens/Frame';
import '../../components/page/globals.css';
import '../../components/page/styleguide.css';

export default function HomePage() {
  const params = useParams();
  const locale = params.locale as string;

  return (
    <div style={{ minHeight: '100vh', width: '100vw', overflowX: 'auto' }}>
      <Frame locale={locale} />
    </div>
  );
}