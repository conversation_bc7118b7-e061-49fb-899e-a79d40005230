.header {
  align-items: center;
  background: linear-gradient(
    272deg,
    rgba(242, 211, 164, 1) 0%,
    rgba(217, 217, 217, 1) 100%
  );
  background-color: transparent;
  border-radius: 0px 0px 200px 0px;
  display: flex;
  flex-direction: column;
  height: 940px;
  left: 0;
  overflow: hidden;
  padding: 80px 0px;
  position: absolute;
  top: 0;
  width: 100%;
  max-width: 1920px;
  margin: 0 auto;
}

.header .bathing {
  height: 109px;
  left: 1743px;
  object-fit: cover;
  position: absolute;
  top: 264px;
  width: 109px;
}

.header .slice {
  height: 1px;
  position: relative;
  width: 1px;
}

.header .container {
  height: 640px;
  max-width: 1536px;
  position: relative;
  width: 1536px;
}

.header .overlap-group {
  height: 137px;
  left: 61px;
  position: absolute;
  top: -21px;
  width: 1411px;
}

.header .nav {
  height: 48px;
  left: 3px;
  position: absolute;
  top: 21px;
  width: 1408px;
}

.header .link-back-to-home {
  color: #262626;
  font-family: "Manrope", Helvetica;
  font-size: 20px;
  font-weight: 500;
  height: 24px;
  left: 820px;
  letter-spacing: 0;
  line-height: 24px;
  position: absolute;
  top: 11px;
  white-space: nowrap;
  width: 130px;
  text-decoration: none;
  transition: color 0.2s ease;
}

.header .link-back-to-home:hover {
  color: #A126FF;
}

.header .link-features {
  color: #262626;
  font-family: "Manrope", Helvetica;
  font-size: 20px;
  font-weight: 500;
  height: 24px;
  left: 977px;
  letter-spacing: 0;
  line-height: 24px;
  position: absolute;
  top: 11px;
  white-space: nowrap;
  width: 86px;
}

.header .link-web-store {
  color: #262626;
  font-family: "Manrope", Helvetica;
  font-size: 20px;
  font-weight: 500;
  height: 24px;
  left: 1128px;
  letter-spacing: 0;
  line-height: 24px;
  position: absolute;
  top: 11px;
  white-space: nowrap;
  width: 99px;
}

.header .component-1 {
  background: linear-gradient(
    147deg,
    rgba(161, 38, 255, 1) 0%,
    rgba(96, 23, 153, 1) 100%
  ) !important;
  background-color: unset !important;
  display: flex !important;
  left: 1292px !important;
  position: absolute !important;
  top: 0 !important;
  width: 187px !important;
}

.header .component-instance {
  font-size: 20px !important;
}

.header .onenata {
  height: 137px;
  left: 0;
  object-fit: cover;
  position: absolute;
  top: 0;
  width: 383px;
}

.header .heading {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  left: 64px;
  position: absolute;
  top: 255px;
  width: 672px;
}

.header .all-in-one-platform {
  color: #262626;
  font-family: "Manrope", Helvetica;
  font-size: 60px;
  font-weight: 800;
  letter-spacing: 0;
  line-height: 70px;
  margin-top: -1.00px;
  position: relative;
  width: fit-content;
}

.header .onenata-connects-wrapper {
  align-items: flex-start;
  display: flex;
  flex-direction: column;
  left: 64px;
  position: absolute;
  top: 450px;
  width: 655px;
}

.header .onenata-connects {
  align-self: stretch;
  color: #262626;
  font-family: "Manrope", Helvetica;
  font-size: 24px;
  font-weight: 400;
  letter-spacing: 0.48px;
  line-height: 35px;
  margin-top: -1.00px;
  position: relative;
}

.header .component-2 {
  background: linear-gradient(
    147deg,
    rgba(161, 38, 255, 1) 0%,
    rgba(96, 23, 153, 1) 100%
  ) !important;
  background-color: unset !important;
  height: 60px !important;
  left: 64px !important;
  position: absolute !important;
  top: 600px !important;
}

.header .component-2-instance {
  font-size: 20px !important;
  margin-top: unset !important;
}

.header .component-4-instance {
  background: linear-gradient(
    147deg,
    rgba(161, 38, 255, 1) 0%,
    rgba(96, 23, 153, 1) 100%
  ) !important;
  background-color: unset !important;
  height: 60px !important;
  left: 356px !important;
  position: absolute !important;
  top: 600px !important;
}

.header .design-component-instance-node {
  color: #ffffff !important;
  font-size: 20px !important;
  margin-top: unset !important;
}

.header .food {
  height: 77px;
  left: 1466px;
  object-fit: cover;
  position: absolute;
  top: 175px;
  width: 77px;
}

.header .img {
  height: 72px;
  left: 1444px;
  object-fit: cover;
  position: absolute;
  top: 268px;
  width: 72px;
}

.header .overlap-wrapper {
  height: 626px;
  left: 1150px;
  overflow: hidden;
  position: absolute;
  top: 217px;
  transform: rotate(-16.07deg);
  width: 532px;
}

.header .overlap {
  height: 644px;
  left: -22px;
  position: relative;
  top: -18px;
  width: 596px;
}

.header .black {
  height: 489px;
  left: 44px;
  position: absolute;
  top: 122px;
  transform: rotate(11.22deg);
  width: 390px;
}

.header .mockup {
  height: 489px;
}

.header .overlap-2 {
  height: 594px;
  left: -66px;
  position: relative;
  top: -52px;
  width: 523px;
}

.header .iphone {
  height: 520px;
  left: 46px;
  position: absolute;
  top: 37px;
  transform: rotate(-11.22deg);
  width: 430px;
}

.header .group {
  height: 460px;
  left: 87px;
  position: absolute;
  top: 59px;
  width: 362px;
}

.header .overlap-group-2 {
  height: 558px;
  left: -63px;
  position: relative;
  top: -49px;
  width: 487px;
}

.header .mask {
  height: 455px;
  left: 46px;
  position: absolute;
  top: 51px;
  transform: rotate(-11.22deg);
  width: 394px;
}

.header .mask-group {
  height: 489px;
  left: 44px;
  position: absolute;
  top: 34px;
  transform: rotate(-11.22deg);
  width: 400px;
}

.header .AI-agent {
  height: 495px;
  left: 82px;
  object-fit: cover;
  position: absolute;
  top: 53px;
  transform: rotate(16.07deg);
  width: 454px;
}

.header .rectangle {
  background-color: #ffffff;
  height: 77px;
  left: 224px;
  position: absolute;
  top: 229px;
  width: 193px;
}

.header .group-2 {
  height: 125px;
  left: 216px;
  position: absolute;
  top: 215px;
  transform: rotate(16.07deg);
  width: 243px;
}

.header .bell {
  height: 51px;
  left: 460px;
  object-fit: cover;
  position: absolute;
  top: 190px;
  transform: rotate(16.07deg);
  width: 51px;
}
