'use client';

import { useTranslations } from 'next-intl';
import { useParams, useRouter } from 'next/navigation';
import { useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import LanguageSelector from '../components/LanguageSelector';
import { useAuth } from '../../lib/firebase/context/AuthContext';

export default function AdminHomePage() {
  const t = useTranslations('HomePage');
  const params = useParams();
  const router = useRouter();
  const locale = params.locale as string;
  const { user, userData, loading } = useAuth();

  // 如果用户已登录，检查邮箱验证状态和个人资料完成状态并重定向
  useEffect(() => {
    if (!loading && user) {
      if (!user.emailVerified) {
        // 邮箱未验证，跳转到邮箱验证页面
        router.push(`/${locale}/auth/verify-email`);
      } else if (!userData?.isProfileCompleted) {
        // 邮箱已验证但个人资料未完成，跳转到个人资料设置页面
        router.push(`/${locale}/auth/profile-setup`);
      } else {
        // 邮箱已验证且个人资料已完成，跳转到仪表板
        router.back();
      }
    }
  }, [user, userData, loading, router, locale]);

  // 显示加载状态
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-[#F2D3A4] via-[#D9D9D9] to-[#F2D3A4]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-[#A126FF] mx-auto mb-4"></div>
          <p className="text-gray-600">{t('loading')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Navigation */}
      <nav className="fixed top-0 left-0 right-0 z-50 bg-white/80 backdrop-blur-xl border-b border-gray-200/50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link href={`/${locale}`} className="flex items-center">
                <div className="relative w-8 h-8 mr-3">
                  <Image
                    src="/images/logo.png"
                    alt="OneNata Logo"
                    fill
                    className="object-contain"
                    priority
                  />
                </div>
                <h1 className="text-xl font-semibold text-gray-900">OneNata Admin</h1>
              </Link>
            </div>
            
            <div className="flex items-center space-x-6">
              <div className="hidden md:flex space-x-8">
                <a href="#features" className="text-gray-600 hover:text-gray-900 transition-colors font-medium">
                  {t('nav.features')}
                </a>
              </div>
              <LanguageSelector currentLocale={locale} />
              <Link
                href={`/${locale}/auth/login`}
                className="inline-flex items-center px-4 py-2 bg-[#A126FF] text-white font-medium rounded-full hover:bg-[#8a20d8] transition-colors"
              >
                {t('nav.signIn')}
              </Link>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="pt-24 pb-16 bg-gradient-to-br from-[#F2D3A4] via-[#D9D9D9] to-[#F2D3A4]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-6">
                <h1 className="text-5xl md:text-6xl font-bold text-gray-900 leading-tight">
                  {t('hero.title')}
                </h1>
                <p className="text-xl md:text-2xl text-gray-700 leading-relaxed max-w-2xl">
                  {t('hero.subtitle')}
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href={`/${locale}/auth/signup`}
                  className="inline-flex items-center justify-center px-8 py-4 bg-[#A126FF] text-white font-semibold text-lg rounded-full hover:bg-[#8a20d8] transition-all duration-200 shadow-lg hover:shadow-xl"
                >
                  {t('hero.startFree')}
                </Link>

                <Link
                  href="#demo"
                  className="inline-flex items-center justify-center px-8 py-4 border-2 border-[#A126FF] text-[#A126FF] font-semibold text-lg rounded-full hover:bg-[#A126FF] hover:text-white transition-all duration-200"
                >
                  {t('hero.watchDemo')}
                </Link>
              </div>

              <div className="flex items-center space-x-6 text-sm text-gray-600">
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  {t('hero.feature1')}
                </div>
                <div className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  {t('hero.feature2')}
                </div>
              </div>
            </div>

            {/* Dashboard Preview */}
            <div className="relative">
              <div className="relative w-full max-w-lg mx-auto">
                <div className="relative transform rotate-[-2deg] hover:rotate-0 transition-transform duration-500">
                  <div className="w-80 h-96 bg-gray-900 rounded-[2rem] p-3 shadow-2xl">
                    <div className="w-full h-full bg-gradient-to-br from-gray-50 to-gray-100 rounded-[1.5rem] p-6 relative overflow-hidden">
                      <div className="absolute inset-0 bg-gradient-to-br from-[#A126FF]/5 to-[#601799]/5"></div>
                      <div className="relative z-10 h-full flex flex-col">
                        <div className="flex items-center justify-between mb-6">
                          <div className="flex items-center space-x-3">
                            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                          </div>
                          <div className="text-xs text-gray-500">OneNata Admin</div>
                        </div>

                        <div className="space-y-4">
                          <div className="h-4 bg-gray-200 rounded-full"></div>
                          <div className="h-4 bg-gray-200 rounded-full w-3/4"></div>
                          <div className="h-4 bg-gray-200 rounded-full w-1/2"></div>

                          <div className="grid grid-cols-2 gap-3 mt-6">
                            <div className="h-16 bg-gradient-to-br from-[#A126FF] to-[#601799] rounded-lg"></div>
                            <div className="h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-lg"></div>
                            <div className="h-16 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg"></div>
                            <div className="h-16 bg-gradient-to-br from-green-500 to-emerald-500 rounded-lg"></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Floating Elements */}
                  <div className="absolute -top-4 -right-4 w-16 h-16 bg-white rounded-full shadow-lg flex items-center justify-center">
                    <div className="w-8 h-8 bg-gradient-to-r from-[#A126FF] to-[#601799] rounded-full"></div>
                  </div>

                  <div className="absolute -bottom-4 -left-4 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center">
                    <div className="w-6 h-6 bg-gradient-to-r from-[#A126FF] to-[#601799] rounded-full"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
