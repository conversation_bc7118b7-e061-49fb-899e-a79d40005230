import React from "react";
import { Component } from "../../../components/Component";
import { Component4 } from "../../../components/Component4";
import { VariantHoverTrueWrapper } from "../../../components/VariantHoverTrueWrapper";
import "./Header.css";

interface HeaderProps {
  locale: string;
}

export const Header = ({ locale }: HeaderProps): JSX.Element => {
  return (
    <header className="header">
      <img
        className="bathing"
        alt="Bathing"
        src="https://c.animaapp.com/9exExmER/img/<EMAIL>"
      />

      <img
        className="slice"
        alt="Slice"
        src="https://c.animaapp.com/9exExmER/img/<EMAIL>"
      />

      <div className="container">
        <div className="overlap-group">
          <div className="nav">
            <a
              href={`/${locale}`}
              className="link-back-to-home"
            >
              Back to Home
            </a>

            <div className="link-features">Features</div>

            <div className="link-web-store">Web Store</div>

            <Component
              className="component-1"
              text="Get the App"
              textClassName="component-instance"
              variant="one"
            />
          </div>

          <img
            className="onenata"
            alt="Onenata"
            src="https://c.animaapp.com/9exExmER/img/<EMAIL>"
          />
        </div>

        <div className="heading">
          <p className="all-in-one-platform">
            All-in-One Platform for
            <br />
            Smart Pet Living
          </p>
        </div>

        <div className="onenata-connects-wrapper">
          <p className="onenata-connects">
            OneNata connects your pet&#39;s world. Track health, explore safely,
            and join a vibrant community of pet lovers.
          </p>
        </div>

        <VariantHoverTrueWrapper
          className="component-2"
          divClassName="component-2-instance"
          variant="one"
        />
        <Component4
          className="component-4-instance"
          text="Get on Google Play"
          textClassName="design-component-instance-node"
          variant="one"
          variant1Color="white"
        />
        <img
          className="food"
          alt="Food"
          src="https://c.animaapp.com/9exExmER/img/<EMAIL>"
        />

        <img
          className="img"
          alt="Food"
          src="https://c.animaapp.com/9exExmER/img/<EMAIL>"
        />
      </div>

      <div className="overlap-wrapper">
        <div className="overlap">
          <div className="black">
            <div className="mockup">
              <div className="overlap-2">
                <img
                  className="iphone"
                  alt="Iphone"
                  src="https://c.animaapp.com/9exExmER/img/<EMAIL>"
                />

                <div className="group">
                  <div className="overlap-group-2">
                    <img
                      className="mask"
                      alt="Mask"
                      src="https://c.animaapp.com/9exExmER/img/mask.svg"
                    />

                    <img
                      className="mask-group"
                      alt="Mask group"
                      src="https://c.animaapp.com/9exExmER/img/<EMAIL>"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <img
            className="AI-agent"
            alt="Ai agent"
            src="https://c.animaapp.com/9exExmER/img/<EMAIL>"
          />

          <div className="rectangle" />

          <img
            className="group-2"
            alt="Group"
            src="https://c.animaapp.com/9exExmER/img/<EMAIL>"
          />

          <img
            className="bell"
            alt="Bell"
            src="https://c.animaapp.com/9exExmER/img/<EMAIL>"
          />
        </div>
      </div>
    </header>
  );
};

